#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف بدء التشغيل المحسن لـ Render
Optimized startup script for Render deployment
"""

import os
import sys
import time
import threading
import requests
import schedule
from datetime import datetime
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RenderKeepAlive:
    """نظام منع النوم على Render"""
    
    def __init__(self):
        self.app_url = os.getenv('RENDER_EXTERNAL_URL', 'https://gaming-news-agent.onrender.com')
        self.ping_interval = 10  # دقائق
        self.is_running = False
        
    def ping_self(self):
        """إرسال ping للتطبيق لمنع النوم"""
        try:
            response = requests.get(f"{self.app_url}/health", timeout=30)
            if response.status_code == 200:
                logger.info(f"✅ Ping successful at {datetime.now()}")
            else:
                logger.warning(f"⚠️ Ping returned status {response.status_code}")
        except Exception as e:
            logger.error(f"❌ Ping failed: {e}")
    
    def start_ping_scheduler(self):
        """بدء جدولة ping التلقائي"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # جدولة ping كل 10 دقائق
        schedule.every(self.ping_interval).minutes.do(self.ping_self)
        
        def run_scheduler():
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
        
        # تشغيل المجدول في thread منفصل
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        logger.info(f"🔄 Keep-alive system started (ping every {self.ping_interval} minutes)")

def setup_render_environment():
    """إعداد البيئة لـ Render"""
    
    # إعداد متغيرات البيئة الافتراضية
    default_env = {
        'PYTHONUNBUFFERED': '1',
        'PYTHONPATH': '/opt/render/project/src',
        'GRADIO_SERVER_NAME': '0.0.0.0',
        'GRADIO_SERVER_PORT': '10000',
        'APP_ENV': 'production',
        'DEBUG': 'false',
        'LOG_LEVEL': 'INFO'
    }
    
    for key, value in default_env.items():
        if key not in os.environ:
            os.environ[key] = value
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['data', 'logs', 'cache', 'images', 'temp']
    for dir_name in required_dirs:
        os.makedirs(dir_name, exist_ok=True)
    
    logger.info("✅ Render environment setup completed")

def check_dependencies():
    """فحص المتطلبات الأساسية"""
    try:
        import gradio
        import google.generativeai
        import requests
        import beautifulsoup4
        logger.info("✅ All dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    logger.info("🚀 Starting Gaming News Agent on Render...")
    
    # إعداد البيئة
    setup_render_environment()
    
    # فحص المتطلبات
    if not check_dependencies():
        logger.error("❌ Dependencies check failed")
        sys.exit(1)
    
    # بدء نظام منع النوم
    keep_alive = RenderKeepAlive()
    keep_alive.start_ping_scheduler()
    
    # تأخير قصير للسماح للنظام بالاستقرار
    time.sleep(5)
    
    try:
        # استيراد وتشغيل التطبيق
        logger.info("📱 Starting Gradio app...")
        
        # إضافة المسار الحالي
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # استيراد التطبيق
        from app import AdminDashboard
        
        # إنشاء وتشغيل التطبيق
        dashboard = AdminDashboard()
        app = dashboard.create_interface()
        
        # تشغيل التطبيق
        app.launch(
            server_name="0.0.0.0",
            server_port=int(os.getenv('PORT', '10000')),
            share=False,
            show_error=True,
            debug=False,
            enable_queue=True,
            max_threads=10
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to start application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
