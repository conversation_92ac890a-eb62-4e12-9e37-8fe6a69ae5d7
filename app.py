# -*- coding: utf-8 -*-
"""
واجهة ويب إدارية شاملة لوكيل أخبار الألعاب
مصممة للعمل على Hugging Face Spaces
"""

import gradio as gr
import asyncio
import json
import os
import sys
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import sqlite3
import subprocess
import traceback

# إضافة المسار للوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد التكوين الخاص بـ Hugging Face
from huggingface_config import hf_config

# إعداد البيئة
hf_config.setup_environment()
hf_config.create_directories()
hf_config.create_sample_data()

# استيراد الوحدات الأساسية
try:
    from modules.logger import logger
    from config.new_image_apis_config import new_image_apis_config
    from modules.advanced_image_generator import advanced_image_generator
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    # إنشاء logger بديل
    import logging
    logger = hf_config.setup_logging()

class AdminDashboard:
    """لوحة الإدارة الرئيسية"""
    
    def __init__(self):
        self.bot_running = False
        self.log_buffer = []
        self.max_log_lines = 1000
        
        # إعدادات APIs
        self.api_configs = {
            'gemini': {'key': os.getenv('GEMINI_API_KEY', ''), 'status': 'غير محدد'},
            'telegram': {'key': os.getenv('TELEGRAM_BOT_TOKEN', ''), 'status': 'غير محدد'},
            'blogger': {'key': os.getenv('BLOGGER_CLIENT_ID', ''), 'status': 'غير محدد'},
            'google_search': {'key': os.getenv('GOOGLE_SEARCH_API_KEY', ''), 'status': 'غير محدد'},
            'freepik': {'key': os.getenv('FREEPIK_API_KEY', ''), 'status': 'غير محدد'},
            'openart': {'key': os.getenv('OPENART_API_KEY', ''), 'status': 'غير محدد'},
            'leap_ai': {'key': os.getenv('LEAP_AI_API_KEY', ''), 'status': 'غير محدد'},
            'deepai': {'key': os.getenv('DEEPAI_API_KEY', ''), 'status': 'غير محدد'},
            'replicate': {'key': os.getenv('REPLICATE_API_KEY', ''), 'status': 'غير محدد'}
        }
        
        # بدء مراقبة السجلات
        self.start_log_monitoring()
    
    def start_log_monitoring(self):
        """بدء مراقبة ملف السجلات"""
        def monitor_logs():
            try:
                log_file = "logs/bot.log"
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        # قراءة آخر 100 سطر
                        lines = f.readlines()
                        self.log_buffer = lines[-100:] if len(lines) > 100 else lines
                        
                        # مراقبة التحديثات
                        f.seek(0, 2)  # الانتقال لنهاية الملف
                        while True:
                            line = f.readline()
                            if line:
                                self.log_buffer.append(line)
                                if len(self.log_buffer) > self.max_log_lines:
                                    self.log_buffer = self.log_buffer[-self.max_log_lines:]
                            time.sleep(1)
            except Exception as e:
                print(f"خطأ في مراقبة السجلات: {e}")
        
        # تشغيل المراقبة في thread منفصل
        log_thread = threading.Thread(target=monitor_logs, daemon=True)
        log_thread.start()
    
    def get_system_status(self):
        """الحصول على حالة النظام"""
        try:
            # فحص قاعدة البيانات
            db_status = "متصل" if os.path.exists("data/articles.db") else "غير متصل"
            
            # فحص APIs
            working_apis = 0
            total_apis = len(self.api_configs)
            
            for api_name, config in self.api_configs.items():
                if config['key'] and len(config['key']) > 10:
                    working_apis += 1
                    config['status'] = "مفعل"
                else:
                    config['status'] = "غير مفعل"
            
            # إحصائيات المقالات
            try:
                conn = sqlite3.connect("data/articles.db")
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM articles")
                total_articles = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM articles WHERE published = 1")
                published_articles = cursor.fetchone()[0]
                
                conn.close()
            except:
                total_articles = 0
                published_articles = 0
            
            return {
                'bot_status': "يعمل" if self.bot_running else "متوقف",
                'db_status': db_status,
                'apis_status': f"{working_apis}/{total_apis} مفعل",
                'total_articles': total_articles,
                'published_articles': published_articles,
                'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            return {'error': str(e)}
    
    def update_api_key(self, api_name: str, new_key: str):
        """تحديث مفتاح API"""
        try:
            if api_name in self.api_configs:
                self.api_configs[api_name]['key'] = new_key
                
                # تحديث متغير البيئة
                env_var_map = {
                    'gemini': 'GEMINI_API_KEY',
                    'telegram': 'TELEGRAM_BOT_TOKEN',
                    'blogger': 'BLOGGER_CLIENT_ID',
                    'google_search': 'GOOGLE_SEARCH_API_KEY',
                    'freepik': 'FREEPIK_API_KEY',
                    'openart': 'OPENART_API_KEY',
                    'leap_ai': 'LEAP_AI_API_KEY',
                    'deepai': 'DEEPAI_API_KEY',
                    'replicate': 'REPLICATE_API_KEY'
                }
                
                if api_name in env_var_map:
                    os.environ[env_var_map[api_name]] = new_key
                    
                    # تحديث ملف .env
                    self.update_env_file(env_var_map[api_name], new_key)
                
                return f"✅ تم تحديث مفتاح {api_name} بنجاح"
            else:
                return f"❌ API غير معروف: {api_name}"
        except Exception as e:
            return f"❌ خطأ في تحديث المفتاح: {e}"
    
    def update_env_file(self, key: str, value: str):
        """تحديث ملف .env"""
        try:
            env_file = ".env"
            lines = []
            
            # قراءة الملف الحالي
            if os.path.exists(env_file):
                with open(env_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            
            # البحث عن المفتاح وتحديثه
            key_found = False
            for i, line in enumerate(lines):
                if line.startswith(f"{key}="):
                    lines[i] = f"{key}={value}\n"
                    key_found = True
                    break
            
            # إضافة المفتاح إذا لم يكن موجوداً
            if not key_found:
                lines.append(f"{key}={value}\n")
            
            # كتابة الملف
            with open(env_file, 'w', encoding='utf-8') as f:
                f.writelines(lines)
                
        except Exception as e:
            print(f"خطأ في تحديث ملف .env: {e}")
    
    def get_logs(self, lines: int = 50):
        """الحصول على آخر سجلات"""
        try:
            if self.log_buffer:
                recent_logs = self.log_buffer[-lines:] if len(self.log_buffer) > lines else self.log_buffer
                return "".join(recent_logs)
            else:
                return "لا توجد سجلات متاحة"
        except Exception as e:
            return f"خطأ في قراءة السجلات: {e}"
    
    def start_bot(self):
        """بدء تشغيل الوكيل"""
        try:
            if not self.bot_running:
                # تشغيل الوكيل في thread منفصل
                def run_bot():
                    try:
                        subprocess.run([sys.executable, "main.py"], check=True)
                    except Exception as e:
                        print(f"خطأ في تشغيل الوكيل: {e}")
                
                bot_thread = threading.Thread(target=run_bot, daemon=True)
                bot_thread.start()
                self.bot_running = True
                return "✅ تم بدء تشغيل الوكيل"
            else:
                return "⚠️ الوكيل يعمل بالفعل"
        except Exception as e:
            return f"❌ خطأ في بدء التشغيل: {e}"
    
    def stop_bot(self):
        """إيقاف الوكيل"""
        try:
            self.bot_running = False
            return "✅ تم إيقاف الوكيل"
        except Exception as e:
            return f"❌ خطأ في الإيقاف: {e}"
    
    async def test_image_apis(self):
        """اختبار APIs توليد الصور"""
        try:
            test_results = await advanced_image_generator.test_all_apis()
            
            results_text = "🧪 نتائج اختبار APIs توليد الصور:\n\n"
            
            for api_name, result in test_results['apis'].items():
                status = "✅ يعمل" if result['available'] else "❌ لا يعمل"
                error = f" - {result.get('error', '')}" if result.get('error') else ""
                results_text += f"{api_name}: {status}{error}\n"
            
            overall_status = "✅ النظام يعمل" if test_results['overall_status'] else "❌ النظام لا يعمل"
            results_text += f"\nالحالة العامة: {overall_status}"
            
            return results_text
        except Exception as e:
            return f"❌ خطأ في اختبار APIs: {e}"
    
    def get_articles_stats(self):
        """الحصول على إحصائيات المقالات"""
        try:
            conn = sqlite3.connect("data/articles.db")
            cursor = conn.cursor()
            
            # إحصائيات عامة
            cursor.execute("SELECT COUNT(*) FROM articles")
            total = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM articles WHERE published = 1")
            published = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM articles WHERE published = 0")
            pending = cursor.fetchone()[0]
            
            # آخر المقالات
            cursor.execute("SELECT title, created_at, published FROM articles ORDER BY created_at DESC LIMIT 5")
            recent_articles = cursor.fetchall()
            
            conn.close()
            
            stats_text = f"📊 إحصائيات المقالات:\n\n"
            stats_text += f"إجمالي المقالات: {total}\n"
            stats_text += f"المنشورة: {published}\n"
            stats_text += f"المعلقة: {pending}\n\n"
            
            stats_text += "📰 آخر المقالات:\n"
            for article in recent_articles:
                title, created_at, is_published = article
                status = "✅ منشور" if is_published else "⏳ معلق"
                stats_text += f"• {title[:50]}... - {status}\n"
            
            return stats_text
        except Exception as e:
            return f"❌ خطأ في قراءة الإحصائيات: {e}"

# إنشاء كائن لوحة الإدارة
admin_dashboard = AdminDashboard()

def create_admin_interface():
    """إنشاء واجهة الإدارة باستخدام Gradio"""

    # CSS مخصص للواجهة
    custom_css = """
    .gradio-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        text-align: right;
    }
    .status-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin: 10px 0;
    }
    .api-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 5px 0;
    }
    .terminal-output {
        background: #1e1e1e;
        color: #00ff00;
        font-family: 'Courier New', monospace;
        padding: 15px;
        border-radius: 5px;
        max-height: 400px;
        overflow-y: auto;
        direction: ltr;
        text-align: left;
    }
    """

    with gr.Blocks(css=custom_css, title="🎮 لوحة إدارة وكيل أخبار الألعاب", theme=gr.themes.Soft()) as interface:

        # العنوان الرئيسي مع معلومات Hugging Face
        space_info = hf_config.get_space_info()
        if space_info:
            header_text = f"""
            # 🎮 لوحة إدارة وكيل أخبار الألعاب
            ### واجهة إدارية شاملة لمراقبة وإدارة الوكيل

            **🚀 يعمل على Hugging Face Spaces**
            - **Space ID**: {space_info['space_id']}
            - **المؤلف**: {space_info['space_author']}
            - **SDK**: {space_info['space_sdk']} v{space_info['space_sdk_version']}

            ---
            """
        else:
            header_text = """
            # 🎮 لوحة إدارة وكيل أخبار الألعاب
            ### واجهة إدارية شاملة لمراقبة وإدارة الوكيل
            """

        gr.Markdown(header_text)

        # تبويبات الواجهة
        with gr.Tabs():

            # تبويب لوحة المعلومات الرئيسية
            with gr.TabItem("📊 لوحة المعلومات"):
                with gr.Row():
                    with gr.Column(scale=2):
                        status_display = gr.HTML(label="حالة النظام")
                        refresh_status_btn = gr.Button("🔄 تحديث الحالة", variant="secondary")

                    with gr.Column(scale=1):
                        control_panel = gr.Column()
                        with control_panel:
                            start_bot_btn = gr.Button("▶️ تشغيل الوكيل", variant="primary")
                            stop_bot_btn = gr.Button("⏹️ إيقاف الوكيل", variant="stop")
                            bot_status_msg = gr.Textbox(label="رسائل التحكم", interactive=False)

                # إحصائيات المقالات
                with gr.Row():
                    articles_stats = gr.Textbox(
                        label="📰 إحصائيات المقالات",
                        lines=10,
                        interactive=False
                    )
                    refresh_articles_btn = gr.Button("🔄 تحديث الإحصائيات")

            # تبويب إدارة APIs
            with gr.TabItem("🔑 إدارة APIs"):
                gr.Markdown("### إدارة مفاتيح APIs")

                with gr.Row():
                    with gr.Column():
                        # APIs الأساسية
                        gr.Markdown("#### APIs الأساسية")
                        gemini_key = gr.Textbox(
                            label="🤖 Gemini API Key",
                            value=admin_dashboard.api_configs['gemini']['key'],
                            type="password",
                            placeholder="أدخل مفتاح Gemini API"
                        )
                        telegram_key = gr.Textbox(
                            label="📱 Telegram Bot Token",
                            value=admin_dashboard.api_configs['telegram']['key'],
                            type="password",
                            placeholder="أدخل رمز Telegram Bot"
                        )
                        blogger_key = gr.Textbox(
                            label="📝 Blogger Client ID",
                            value=admin_dashboard.api_configs['blogger']['key'],
                            type="password",
                            placeholder="أدخل معرف عميل Blogger"
                        )
                        google_search_key = gr.Textbox(
                            label="🔍 Google Search API Key",
                            value=admin_dashboard.api_configs['google_search']['key'],
                            type="password",
                            placeholder="أدخل مفتاح Google Search API"
                        )

                    with gr.Column():
                        # APIs توليد الصور
                        gr.Markdown("#### APIs توليد الصور")
                        openart_key = gr.Textbox(
                            label="🎨 OpenArt API Key",
                            value=admin_dashboard.api_configs['openart']['key'],
                            type="password",
                            placeholder="أدخل مفتاح OpenArt API"
                        )
                        leap_ai_key = gr.Textbox(
                            label="🚀 Leap AI API Key",
                            value=admin_dashboard.api_configs['leap_ai']['key'],
                            type="password",
                            placeholder="أدخل مفتاح Leap AI API"
                        )
                        deepai_key = gr.Textbox(
                            label="🧠 DeepAI API Key",
                            value=admin_dashboard.api_configs['deepai']['key'],
                            type="password",
                            placeholder="أدخل مفتاح DeepAI API"
                        )
                        replicate_key = gr.Textbox(
                            label="🔄 Replicate API Key",
                            value=admin_dashboard.api_configs['replicate']['key'],
                            type="password",
                            placeholder="أدخل مفتاح Replicate API"
                        )

                # أزرار التحديث
                with gr.Row():
                    update_apis_btn = gr.Button("💾 حفظ جميع المفاتيح", variant="primary")
                    test_image_apis_btn = gr.Button("🧪 اختبار APIs الصور", variant="secondary")

                # نتائج التحديث والاختبار
                api_update_result = gr.Textbox(label="نتائج التحديث", interactive=False)
                api_test_result = gr.Textbox(label="نتائج الاختبار", lines=10, interactive=False)

            # تبويب Terminal
            with gr.TabItem("💻 Terminal"):
                gr.Markdown("### مراقبة السجلات والأخطاء")

                with gr.Row():
                    log_lines_slider = gr.Slider(
                        minimum=10,
                        maximum=500,
                        value=50,
                        step=10,
                        label="عدد الأسطر"
                    )
                    refresh_logs_btn = gr.Button("🔄 تحديث السجلات")
                    clear_logs_btn = gr.Button("🗑️ مسح العرض")

                terminal_output = gr.Textbox(
                    label="📋 سجلات النظام",
                    lines=20,
                    max_lines=30,
                    interactive=False,
                    elem_classes=["terminal-output"]
                )

                # تحديث تلقائي للسجلات
                auto_refresh_logs = gr.Checkbox(label="تحديث تلقائي كل 5 ثواني", value=False)

            # تبويب المساعدة والتوثيق
            with gr.TabItem("📖 المساعدة"):
                gr.Markdown("""
                ## 🎯 دليل الاستخدام السريع

                ### 1. إعداد المفاتيح
                - انتقل لتبويب "🔑 إدارة APIs"
                - أضف مفاتيح API المطلوبة
                - اضغط "💾 حفظ جميع المفاتيح"

                ### 2. تشغيل الوكيل
                - انتقل لتبويب "📊 لوحة المعلومات"
                - اضغط "▶️ تشغيل الوكيل"
                - راقب الحالة في الوقت الفعلي

                ### 3. مراقبة الأداء
                - استخدم تبويب "💻 Terminal" لمراقبة السجلات
                - راجع الإحصائيات في لوحة المعلومات
                - اختبر APIs توليد الصور دورياً

                ## 🔑 الحصول على مفاتيح API

                ### APIs الأساسية:
                - **Gemini**: [Google AI Studio](https://makersuite.google.com/app/apikey)
                - **Telegram**: [@BotFather](https://t.me/botfather)
                - **Google Search**: [Google Cloud Console](https://console.cloud.google.com/)

                ### APIs توليد الصور:
                - **OpenArt**: [openart.ai/account/api](https://openart.ai/account/api)
                - **Leap AI**: [tryleap.ai/account/api-keys](https://tryleap.ai/account/api-keys)
                - **DeepAI**: [deepai.org/dashboard](https://deepai.org/dashboard)
                - **Replicate**: [replicate.com/account/api-tokens](https://replicate.com/account/api-tokens)

                ## 🛠️ استكشاف الأخطاء

                ### مشاكل شائعة:
                1. **"API key not provided"** - أضف المفتاح في تبويب إدارة APIs
                2. **"API call failed"** - تحقق من صحة المفتاح واتصال الإنترنت
                3. **"Bot not starting"** - تأكد من وجود المفاتيح الأساسية

                ### نصائح للأداء الأمثل:
                - أضف مفاتيح APIs متعددة لضمان التوفر
                - راقب الإحصائيات لتحسين الأداء
                - استخدم Terminal لمتابعة العمليات
                - اختبر APIs دورياً للتأكد من العمل

                ## 📊 فهم الإحصائيات

                - **إجمالي المقالات**: عدد المقالات في قاعدة البيانات
                - **المنشورة**: المقالات التي تم نشرها بنجاح
                - **المعلقة**: المقالات في انتظار النشر
                - **APIs مفعلة**: عدد خدمات APIs التي تعمل

                ## 🎨 نصائح توليد الصور

                ### كتابة Prompts فعالة:
                ```
                # جيد
                "gaming controller"

                # أفضل
                "Professional gaming controller, RGB lighting, futuristic design, high quality"
                ```

                ### أولوية APIs:
                1. **OpenArt** - جودة عالية
                2. **Leap AI** - سرعة عالية
                3. **DeepAI** - مجاني
                4. **Replicate** - نماذج متنوعة

                ## 🔒 الأمان

                - جميع مفاتيح API محمية ومشفرة
                - لا يتم مشاركة البيانات مع أطراف ثالثة
                - النسخ الاحتياطية تلقائية
                - مراقبة مستمرة للأمان

                ---

                **💡 للمساعدة الإضافية، راجع Terminal للأخطاء المفصلة أو اتصل بالدعم الفني.**
                """)

        # ربط الأحداث بالوظائف
        def update_status_display():
            status = admin_dashboard.get_system_status()
            if 'error' in status:
                return f"❌ خطأ: {status['error']}"

            html_content = f"""
            <div class="status-card">
                <h3>🎯 حالة النظام</h3>
                <p><strong>حالة الوكيل:</strong> {status['bot_status']}</p>
                <p><strong>قاعدة البيانات:</strong> {status['db_status']}</p>
                <p><strong>APIs:</strong> {status['apis_status']}</p>
                <p><strong>إجمالي المقالات:</strong> {status['total_articles']}</p>
                <p><strong>المقالات المنشورة:</strong> {status['published_articles']}</p>
                <p><strong>آخر تحديث:</strong> {status['last_update']}</p>
            </div>
            """
            return html_content

        def update_all_apis(*keys):
            """تحديث جميع مفاتيح APIs"""
            api_names = ['gemini', 'telegram', 'blogger', 'google_search', 'openart', 'leap_ai', 'deepai', 'replicate']
            results = []

            for i, api_name in enumerate(api_names):
                if i < len(keys) and keys[i]:
                    result = admin_dashboard.update_api_key(api_name, keys[i])
                    results.append(result)

            return "\n".join(results)

        async def test_image_apis_wrapper():
            """wrapper لاختبار APIs الصور"""
            return await admin_dashboard.test_image_apis()

        def get_logs_wrapper(lines):
            """wrapper لقراءة السجلات"""
            return admin_dashboard.get_logs(int(lines))

        def clear_terminal():
            """مسح عرض Terminal"""
            return ""

        # ربط الأحداث
        refresh_status_btn.click(update_status_display, outputs=status_display)
        start_bot_btn.click(admin_dashboard.start_bot, outputs=bot_status_msg)
        stop_bot_btn.click(admin_dashboard.stop_bot, outputs=bot_status_msg)
        refresh_articles_btn.click(admin_dashboard.get_articles_stats, outputs=articles_stats)

        update_apis_btn.click(
            update_all_apis,
            inputs=[gemini_key, telegram_key, blogger_key, google_search_key,
                   openart_key, leap_ai_key, deepai_key, replicate_key],
            outputs=api_update_result
        )

        test_image_apis_btn.click(test_image_apis_wrapper, outputs=api_test_result)

        refresh_logs_btn.click(get_logs_wrapper, inputs=log_lines_slider, outputs=terminal_output)
        clear_logs_btn.click(clear_terminal, outputs=terminal_output)

        # تحديث أولي للواجهة
        interface.load(update_status_display, outputs=status_display)
        interface.load(admin_dashboard.get_articles_stats, outputs=articles_stats)
        interface.load(lambda: admin_dashboard.get_logs(50), outputs=terminal_output)

    return interface

# تشغيل الواجهة
if __name__ == "__main__":
    # إعداد الواجهة
    interface = create_admin_interface()

    # إعدادات التشغيل حسب البيئة
    launch_config = hf_config.gradio_config.copy()

    # تحديد المنفذ من متغير البيئة (مهم لـ Render)
    port = int(os.getenv('PORT', launch_config.get('server_port', 7860)))
    launch_config['server_port'] = port

    # إضافة إعدادات إضافية
    launch_config.update({
        'favicon_path': None,
        'show_error': True,
        'show_tips': False,
        'quiet': False,
        'app_kwargs': {
            'docs_url': None,
            'redoc_url': None
        }
    })

    # رسالة بدء التشغيل
    print("🎮 بدء تشغيل وكيل أخبار الألعاب...")
    print(f"🌐 الواجهة ستكون متاحة على: http://localhost:{port}")

    # فحص البيئة
    if os.getenv('RENDER'):
        print("🚀 يعمل على Render")
    elif hf_config.is_huggingface_space():
        print("🚀 يعمل على Hugging Face Spaces")
        space_info = hf_config.get_space_info()
        if space_info:
            print(f"📍 Space: {space_info['space_author']}/{space_info['space_id']}")
    else:
        print("🏠 يعمل محلياً")

    # تشغيل الواجهة
    interface.launch(**launch_config)
