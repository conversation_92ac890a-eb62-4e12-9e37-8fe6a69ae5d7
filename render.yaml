# ملف تكوين Render لوكيل أخبار الألعاب
# Gaming News Agent - Render Configuration

services:
  # خدمة الويب الرئيسية
  - type: web
    name: gaming-news-agent
    env: python
    plan: free
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
    startCommand: python app.py
    envVars:
      # إعدادات Python
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PYTHONUNBUFFERED
        value: 1
      - key: PYTHONPATH
        value: /opt/render/project/src
      
      # إعدادات Gradio
      - key: GRADIO_SERVER_NAME
        value: 0.0.0.0
      - key: GRADIO_SERVER_PORT
        value: 10000
      - key: GRADIO_SHARE
        value: false
      
      # إعدادات التطبيق
      - key: APP_ENV
        value: production
      - key: DEBUG
        value: false
      - key: LOG_LEVEL
        value: INFO
      
      # إعدادات قاعدة البيانات
      - key: DATABASE_URL
        value: sqlite:///data/articles.db
      
      # إعدادات الأمان
      - key: SECRET_KEY
        generateValue: true
      
      # مفاتيح API (يجب إضافتها يدوياً في لوحة Render)
      # - key: GEMINI_API_KEY
      #   sync: false
      # - key: TELEGRAM_BOT_TOKEN
      #   sync: false
      # - key: BLOGGER_CLIENT_ID
      #   sync: false
      # - key: BLOGGER_CLIENT_SECRET
      #   sync: false
      # - key: OPENART_API_KEY
      #   sync: false
      # - key: LEAP_AI_API_KEY
      #   sync: false
      # - key: DEEPAI_API_KEY
      #   sync: false
      # - key: REPLICATE_API_TOKEN
      #   sync: false
      # - key: NEWSDATA_API_KEY
      #   sync: false
      # - key: SERPAPI_KEY
      #   sync: false
      # - key: TAVILY_API_KEY
      #   sync: false
    
    # إعدادات الصحة والمراقبة
    healthCheckPath: /health
    
    # إعدادات الموارد
    disk:
      name: gaming-news-data
      mountPath: /opt/render/project/src/data
      sizeGB: 1
    
    # إعدادات الشبكة
    domains:
      - gaming-news-agent.onrender.com
    
    # إعدادات إعادة التشغيل
    autoDeploy: true
    
    # متغيرات البناء
    buildFilter:
      paths:
        - "**/*.py"
        - "requirements.txt"
        - "render.yaml"
      ignoredPaths:
        - "**/__pycache__/**"
        - "**/*.pyc"
        - "**/logs/**"
        - "**/temp/**"
        - "**/test_*"
        - "**/.git/**"

# إعدادات عامة
region: oregon
