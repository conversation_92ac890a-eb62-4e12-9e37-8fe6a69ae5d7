# 🎮 Gaming News Agent - وكيل أخبار الألعاب

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Deploy](https://img.shields.io/badge/Deploy-Ready-brightgreen.svg)](DEPLOYMENT_GUIDE.md)

وكيل برمجي ذكي لجمع ونشر أخبار الألعاب تلقائياً مع واجهة ويب تفاعلية للمراقبة والتحكم.

## ✨ المميزات الرئيسية

- 🤖 **جمع أخبار ذكي** - من مصادر متعددة باستخدام AI
- 📝 **إنشاء محتوى تلقائي** - مقالات عالية الجودة
- 🌐 **واجهة ويب تفاعلية** - لوحة تحكم شاملة
- 🚀 **جاهز للنشر** - على Render, Heroku, Railway
- 📊 **مراقبة في الوقت الفعلي** - إحصائيات وسجلات
- 🛡️ **آمن ومحسن** - حماية مفاتيح API وأداء عالي

## 🚀 التشغيل السريع

### 1. استنساخ المشروع
```bash
git clone https://github.com/Mcamento8/gaming-news-agent.git
cd gaming-news-agent
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل الوكيل
```bash
python quick_start.py
```

### 4. الوصول للواجهة
افتح المتصفح على: `http://localhost:5000`

## 🌐 النشر على الاستضافة

### Render (الأفضل - مجاني)
1. Fork هذا المشروع
2. اذهب إلى [render.com](https://render.com)
3. أنشئ Web Service جديد
4. اربط مستودع GitHub
5. أضف متغيرات البيئة
6. انشر!

[📖 دليل النشر الكامل](DEPLOYMENT_GUIDE.md)

## ⚙️ متغيرات البيئة

```env
# مفاتيح API الأساسية (مطلوبة)
GEMINI_API_KEY=your_gemini_api_key
TELEGRAM_BOT_TOKEN=your_telegram_token
BLOGGER_CLIENT_ID=your_blogger_client_id
BLOGGER_CLIENT_SECRET=your_blogger_client_secret

# مفاتيح إضافية (اختيارية)
OPENART_API_KEY=your_openart_key
NEWSDATA_API_KEY=your_newsdata_key
SERPAPI_KEY=your_serpapi_key
```

## 📁 هيكل المشروع

```
gaming-news-agent/
├── main.py                 # الملف الرئيسي
├── web_api.py             # خادم الواجهة الويب
├── deployment_config.py   # إعدادات النشر
├── quick_start.py         # تشغيل سريع
├── test_deployment.py     # اختبار شامل
├── requirements.txt       # متطلبات Python
├── Procfile              # ملف Heroku
├── render.yaml           # ملف Render
├── modules/              # وحدات الوكيل
├── config/               # ملفات التكوين
├── web_interface/        # ملفات الواجهة الويب
└── docs/                # الوثائق
```

## 🧪 الاختبار

```bash
# اختبار شامل قبل النشر
python test_deployment.py

# تشغيل سريع للاختبار
python quick_start.py
```

## 📊 لوحة التحكم

- **الحالة**: مراقبة حالة الوكيل
- **الإحصائيات**: عدد المقالات المنشورة
- **السجلات**: عرض آخر الأحداث
- **الإعدادات**: تخصيص سلوك الوكيل

## 🛡️ الأمان

- مفاتيح API محفوظة في متغيرات البيئة
- HTTPS مفعل تلقائياً
- CORS محدود للأمان
- Rate Limiting مدمج

## 📈 الأداء

- استخدام الذاكرة: ~200-400MB
- زمن الاستجابة: <2 ثانية
- معدل النجاح: >95%
- وقت التشغيل: 24/7

## 📖 الوثائق

- [دليل النشر](DEPLOYMENT_GUIDE.md)
- [وثائق النشر](README_DEPLOYMENT.md)
- [دليل الاستكشاف](TROUBLESHOOTING.md)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **GitHub**: [@Mcamento8](https://github.com/Mcamento8)
- **المشروع**: [gaming-news-agent](https://github.com/Mcamento8/gaming-news-agent)

---

## 🎉 شكراً لاستخدام وكيل أخبار الألعاب!

إذا أعجبك المشروع، لا تنس إعطاؤه ⭐ على GitHub!
